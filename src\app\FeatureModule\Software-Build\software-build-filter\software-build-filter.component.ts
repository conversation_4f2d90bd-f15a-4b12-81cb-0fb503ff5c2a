import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { forkJoin, Subscription } from 'rxjs';
import { CLIENT_DEVICE, DEMO_DEVICE, NotAssociated, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_ERROR_MESSAGE, SPECIAL_CHARACTER_PATTERN } from 'src/app/app.constants';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { SoftwareBuildSearchRequestBody } from 'src/app/model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { VideoService } from 'src/app/shared/videoservice/video.service';
import { SoftwareBuildOperationsService } from '../software-build-services/software-build-operations.service';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { SoftwareBuildFilterAction } from 'src/app/model/SoftwaarBuilds/SoftwareFilterAction.model';
import { isNullOrUndefined, isUndefined } from 'is-what';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { SoftwareBuildStatusEnum } from 'src/app/shared/enum/SoftwareBuildStatusEnum';

@Component({
  selector: 'app-software-build-filter',
  templateUrl: './software-build-filter.component.html',
  styleUrl: './software-build-filter.component.css'
})
export class SoftwareBuildFilterComponent implements OnInit, OnDestroy {

  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("softwareBuildSearchRequestBody") softwareBuildSearchRequestBody: SoftwareBuildSearchRequestBody;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean;

  constructor(private fb: FormBuilder,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private videoService: VideoService,
    private countryCacheService: CountryCacheService,
    private exceptionService: ExceptionHandlingService,
    private softwareBuildOperationsService: SoftwareBuildOperationsService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
  ) { }

  textBoxMaxLength: number = SMALL_TEXTBOX_MAX_LENGTH;
  textBoxMaxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;

  dropdownSettingsInventoryStatus: MultiSelectDropdownSettings;
  dropdownSettingsDeviceType: MultiSelectDropdownSettings;
  dropdownSettingsJsonVersions: MultiSelectDropdownSettings;
  dropdownSettingsCountry: MultiSelectDropdownSettings;

  countryList: CountryListResponse[] = [];
  jsonVersionList: Array<Jsonlist> = [];
  userAssociatedCountrys: CountryListResponse[] = [];
  deviceTypes: string[];
  inventoryStatus: string[];

  // Subscriptions
  subscriptionForRefreshList: Subscription;
  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);


  filterForm = this.fb.group({
    itemNumber: new FormControl('', [Validators.maxLength(this.textBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    deviceType: new FormControl([], []),
    country: new FormControl([], []),
    jsonVersions: new FormControl([], []),
    inventoryStatus: new FormControl([], []),
    partNumber: new FormControl('', [Validators.maxLength(this.textBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)])
  });


  /**
  * Initialize the component
  *
  * <AUTHOR>
  */
  public ngOnInit(): void {
    this.initializeDropdownSettings();
    this.getInitCall();
    this.onInitSubject();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
  * Initialize dropdown settings
  * <AUTHOR>
  */
  private initializeDropdownSettings(): void {
    this.dropdownSettingsCountry = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, false);
    this.dropdownSettingsDeviceType = this.multiSelectDropDownSettingService.getDeviceTypeDropdownSetting();
    this.dropdownSettingsInventoryStatus = this.multiSelectDropDownSettingService.getSoftwareStatusDropdownSetting();
    this.dropdownSettingsJsonVersions = this.multiSelectDropDownSettingService.getjsonVersionDropdownSetting(false);
  }

  /**
  * Get initial data for dropdowns
  * <AUTHOR>
  */
  public async getInitCall(): Promise<void> {
    this.deviceTypes = [CLIENT_DEVICE, DEMO_DEVICE, deviceTypesEnum.ABOVE_BOTH, NotAssociated];
    this.inventoryStatus = [SoftwareBuildStatusEnum.ACTIVE, SoftwareBuildStatusEnum.INACTIVE];

    if (this.softwareBuildOperationsService.getJsonVersionList().length === 0) {
      this.getJsonVersion();
    } else {
      this.jsonVersionList = this.softwareBuildOperationsService.getJsonVersionList();
    }
    if (this.softwareBuildOperationsService.getCountryList().length === 0) {
      await this.getCountryList();
    } else {
      this.countryList = this.softwareBuildOperationsService.getCountryList();
    }
    this.setFilterValue();
  }

  /**
  * Get package versions for filter dropdown and cache the result
  * @returns Promise<void>
  * <AUTHOR>
  */
  private async getJsonVersion(): Promise<void> {
    this.videoService.getListofJsonVersions()?.subscribe({
      next: (res) => {
        this.jsonVersionList = this.commonsService.checkForNull(res.body);
        // Cache the result in the service
        this.softwareBuildOperationsService.setJsonVersionList(this.jsonVersionList);
      },
      error: (error) => {
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
  * Get country list for filter dropdown and cache the result
  * @returns Promise<void>
  * <AUTHOR>
  */
  private async getCountryList(): Promise<void> {
    this.countryList = await this.countryCacheService.getCountryListFromCache(true);
    // Cache the result in the service
    this.softwareBuildOperationsService.setCountryList(this.countryList);
  }

  /**
  * Initialize subject subscriptions
  * <AUTHOR>
  */
  public onInitSubject(): void {
    this.subscriptionForRefreshList = this.softwareBuildOperationsService?.getSoftwareBuildListRefreshSubject()?.subscribe(
      (listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
        if (listingPageReloadSubjectParameter.isReloadData) {
          if (listingPageReloadSubjectParameter.isClearFilter) {
            this.clearFilter(listingPageReloadSubjectParameter);
          } else {
            //page change 1,2,3
            this.softWareBuildPageRefresh(listingPageReloadSubjectParameter)
          }
        }
      }
    );
  }

  /**
  * Clear all filters and reload data
  * <AUTHOR>
  */
  public clearFilter(listingPageReloadSubjectParameter?: ListingPageReloadSubjectParameter): void {
    this.filterForm.reset();
    this.softWareBuildPageRefresh(listingPageReloadSubjectParameter)
  }

  /**
  * Get Filter Data and pass to Listing page and Reload Page 
  * <AUTHOR>
  */
  private softWareBuildPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterForm.invalid) {
      this.filterForm.reset();
    }
    this.filterForm.get('itemNumber').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('itemNumber').value));
    this.filterForm.get('partNumber').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('partNumber').value));
    let itemNumber = this.filterForm.get('itemNumber').value;
    let countryValue = this.filterForm.get("country").value;
    let jsonVersionValue = this.filterForm.get("jsonVersions").value;
    let countryIds: number[] = isNullOrUndefined(countryValue) ? null : this.commonsService.getIdsFromArray(countryValue);
    let jsonVersionIds: number[] = isNullOrUndefined(jsonVersionValue) ? null : this.commonsService.getIdsFromArray(jsonVersionValue);
    let isActive = this.commonsService.getValuesFromArray(this.filterForm, 'inventoryStatus');
    let deviceType = this.commonsService.getDeviceTypeFilterValueArray(this.filterForm, 'deviceType');
    let partNumber = this.filterForm.get('partNumber').value;
    let requestObject = new SoftwareBuildSearchRequestBody(itemNumber, countryIds, deviceType, isActive, jsonVersionIds, partNumber);
    let roleFilterAction = new SoftwareBuildFilterAction(listingPageReloadSubjectParameter, requestObject);
    this.softwareBuildOperationsService.callSoftwareBuildListFilterRequestParameterSubject(roleFilterAction);
  }

  /**
  * Filter Value of Software build
  */
  public searchInventoryFilter(): void {
    debugger
    if (this.commonsService.checkNullFieldValue(this.filterForm.get('itemNumber').value) == null &&
      this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('deviceType').value[0]) &&
      this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('country').value) &&
      this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('jsonVersions').value) &&
      this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('inventoryStatus').value) &&
      this.commonsService.checkNullFieldValue(this.filterForm.get('partNumber').value) == null) {
      this.commonOperationsService.showEmptyFilterTosteMessge();
    }
    else {
      this.softWareBuildPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefreshList)) { this.subscriptionForRefreshList.unsubscribe() }
  }


  /**
  * Set Old Filter Value
  * 
  * <AUTHOR>
  */
  private setFilterValue() {
    if (this.softwareBuildSearchRequestBody != null) {
      this.filterForm.get('itemNumber').setValue(this.softwareBuildSearchRequestBody.version);
      const deviceType = [this.commonsService.getDeviceTypeEnumToString(this.softwareBuildSearchRequestBody.deviceTypes)]
      this.filterForm.get('deviceType').setValue(!isNullOrUndefined(deviceType[0]) ? deviceType : []);
      const status = this.softwareBuildSearchRequestBody.isActive ? SoftwareBuildStatusEnum.ACTIVE : SoftwareBuildStatusEnum.INACTIVE;
      if (!isNullOrUndefined(this.softwareBuildSearchRequestBody.isActive)) {
        this.filterForm.get('inventoryStatus').setValue([status]);
      }
      this.filterForm.get('country').setValue(this.commonsService.getDropDownValue(this.countryList, this.softwareBuildSearchRequestBody.countryIds));
      const jeasonVersions = this.softwareBuildSearchRequestBody.jsonIds?.length ?
        this.jsonVersionList.filter(json => this.softwareBuildSearchRequestBody.jsonIds.includes(json.id)) : [];
      this.filterForm.get('jsonVersions').setValue(jeasonVersions);

      this.filterForm.get('partNumber').setValue(this.softwareBuildSearchRequestBody.partNumber);
    }
    if (this.listPageRefreshForbackToDetailPage) {
      this.softWareBuildPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }
}
