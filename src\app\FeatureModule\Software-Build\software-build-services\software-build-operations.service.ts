import { Injectable } from '@angular/core';
import { Subject, firstValueFrom } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { isNullOrUndefined } from 'is-what';
import { SoftwareBuildListResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { SoftwareBuildSearchRequestBody } from 'src/app/model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { SoftwareBuildFilterAction } from 'src/app/model/SoftwaarBuilds/SoftwareFilterAction.model';
import { SoftwareBuildListResponse } from 'src/app/model/SoftwaarBuilds/SoftwareBuildListResponse.model';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';
import { SoftwareBuildApiCallService } from './software-api-call/software-build-api-call.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ModuleValidationServiceService } from 'src/app/shared/util/module-validation-service.service';
import { SoftWareBuildConformationService } from './conformation-software-build-dialog/software-build-conformation.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';

/**
* Software Build Operations Service for centralized operation management
* Follows the same pattern as probe operations service
*
* <AUTHOR>
*/
@Injectable({
  providedIn: 'root'
})
export class SoftwareBuildOperationsService {

  constructor(
    private softwareBuildApiCallService: SoftwareBuildApiCallService,
    private permissionService: PermissionService,
    private exceptionHandlingService: ExceptionHandlingService,
    private moduleValidationService: ModuleValidationServiceService,
    private softWareBuildConformationService: SoftWareBuildConformationService,
    private commonsService: CommonsService,
    private toastrService: ToastrService
  ) { }

  //Loading Status
  private softwareBuildListLoadingSubject = new Subject<boolean>();

  //Refresh Software Build List
  private softwareBuildListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Software Build list filter
  private softwareBuildListFilterRequestParameterSubject = new Subject<SoftwareBuildFilterAction>();

  private jsonVersionList: Array<Jsonlist> = [];
  private countryList: Array<CountryListResponse> = [];

  /**
  * Software Build List Page Loading
  * <AUTHOR>
  * @returns Subject<boolean>
  */
  public getSoftwareBuildListLoadingSubject(): Subject<boolean> {
    return this.softwareBuildListLoadingSubject;
  }

  public callSoftwareBuildListLoadingSubject(status: boolean): void {
    this.softwareBuildListLoadingSubject.next(status);
  }


  /**
  * This function call the subject for loading start and stop
  * <AUTHOR>
  * @param status
  * @param resourceName
  */
  public isLoading(status: boolean, resourceName: string): void {
    if (resourceName == SoftwareBuildListResource) {
      this.callSoftwareBuildListLoadingSubject(status);
    }
  }

  /**
  * Software Build List Page Refresh After some Action Like Create or Update or Delete Device
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getSoftwareBuildListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.softwareBuildListRefreshSubject;
  }

  public callSoftwareBuildListFilterRequestParameterSubject(softwareBuildFilterAction: SoftwareBuildFilterAction): void {
    this.softwareBuildListFilterRequestParameterSubject.next(softwareBuildFilterAction);
  }

  /**
   * Role List Page Refresh After some Action Like Serch parameter add
   * Note : Create or Update or Delete Role After Clear All filter and refresh page 
   * <AUTHOR>
   * @returns RoleList 
   */
  public getSoftwareBuildListFilterRequestParameterSubject(): Subject<SoftwareBuildFilterAction> {
    return this.softwareBuildListFilterRequestParameterSubject;
  }

  /**
  * This function call the subject for reload the page data
  * Note : (SoftwareBuildListResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter
  * @param resourceName
  * @param isFilterHidden
  * @param softwareBuildListFilterRequestBodyApply
  */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean, softwareBuildListFilterRequestBodyApply: SoftwareBuildSearchRequestBody): void {
    if (resourceName == SoftwareBuildListResource) {
      // Always use filter subject for list refresh (same pattern as probe service)
      let softwareBuildRequestBody = new SoftwareBuildSearchRequestBody("", [], null, null, null, null);
      if (!isNullOrUndefined(softwareBuildListFilterRequestBodyApply) && !listingPageReloadSubjectParameter.isClearFilter) {
        softwareBuildRequestBody = softwareBuildListFilterRequestBodyApply;
      }
      let softwareBuildFilterAction = new SoftwareBuildFilterAction(listingPageReloadSubjectParameter, softwareBuildRequestBody);
      this.callSoftwareBuildListFilterRequestParameterSubject(softwareBuildFilterAction);
    }
  }

  /**
  * Set JsonVersionList
  */
  public setJsonVersionList(jsonVersionList: Array<Jsonlist>): void {
    this.jsonVersionList = jsonVersionList;
  }

  /**
   * Get JsonVersionList
   */
  public getJsonVersionList(): Array<Jsonlist> {
    return this.jsonVersionList;
  }

  /**
    * Set Country List
    * <AUTHOR>
    * @param countryList - Array of countries to cache
    */
  public setCountryList(countryList: CountryListResponse[]): void {
    this.countryList = countryList;
  }

  /**
  * Get Country List
  * <AUTHOR>
  * @returns Cached array of countries
  */
  public getCountryList(): CountryListResponse[] {
    return this.countryList;
  }

  // ==================== SOFTWARE BUILD OPERATION METHODS ====================

  /**
  * Software Build Operation Action
  * <AUTHOR>
  * @param operationName
  * @param resourceName
  * @param selectedSoftwareBuildIdList
  * @param selectedSoftwareBuildList
  */
  public changeOperationForSoftwareBuild(operationName: string, resourceName: string, selectedSoftwareBuildIdList: number[], selectedSoftwareBuildList: SoftwareBuildListResponse[]): void {
    if (selectedSoftwareBuildIdList.length == 0) {
      this.toastrService.info("Please Select Software Build(s)");
    } else {
      switch (operationName) {
        case "Map to Client Devices":
          this.mapWithDeviceType(selectedSoftwareBuildIdList, selectedSoftwareBuildList, [deviceTypesEnum.CLIENT_DEVICE], resourceName);
          break;
        case "Map to Demo Devices":
          this.mapWithDeviceType(selectedSoftwareBuildIdList, selectedSoftwareBuildList, [deviceTypesEnum.DEMO_DEVICE], resourceName);
          break;
        case "Map to Both type of Devices":
          this.mapWithDeviceType(selectedSoftwareBuildIdList, selectedSoftwareBuildList, [deviceTypesEnum.CLIENT_DEVICE, deviceTypesEnum.DEMO_DEVICE], resourceName);
          break;
        case "Mark as active":
          this.changeInventoryStatus(selectedSoftwareBuildIdList, selectedSoftwareBuildList, true, resourceName);
          break;
        case "Mark as Inactive":
          this.changeInventoryStatus(selectedSoftwareBuildIdList, selectedSoftwareBuildList, false, resourceName);
          break;
        default:
          break;
      }
    }
  }

  /**
  * Map Software Build with Device Types
  * <AUTHOR>
  * @param softwareBuildIds - Array of software build IDs to map
  * @param selectedSoftwareBuilds - Array of selected software build objects for validation
  * @param deviceTypes - Array of device types to map to
  * @param resourceName - Resource name for validation
  */
  public async mapWithDeviceType(softwareBuildIds: number[], selectedSoftwareBuilds: SoftwareBuildListResponse[], deviceTypes: deviceTypesEnum[], resourceName: string): Promise<void> {
    try {
      // Validate user permissions and country access
      if (!this.validateSoftwareBuildPermissions(softwareBuildIds, selectedSoftwareBuilds, resourceName)) {
        return;
      }

      const confirmed = await this.softWareBuildConformationService.openInventoryOperationModel(
        "Confirm",
        "Cancel",
        "Confirmation",
        this.commonsService.getDeviceTypeMapMessage(deviceTypes)
      );

      if (!confirmed) {
        return;
      }

      this.isLoading(true, resourceName);
      const response = await firstValueFrom(this.softwareBuildApiCallService.mapInventoryWithDeviceType(softwareBuildIds, { deviceType: deviceTypes }));
      this.showSuccessAndRefresh(response.body['message'], resourceName);
      return;

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      this.isLoading(false, resourceName);
      return;
    }
  }

  /**
  * Change Software Build Status (Active/Inactive)
  * <AUTHOR>
  * @param softwareBuildIds - Array of software build IDs to change status
  * @param selectedSoftwareBuilds - Array of selected software build objects for validation
  * @param isActive - true for active, false for inactive
  * @param resourceName - Resource name for validation
  */
  public async changeInventoryStatus(softwareBuildIds: number[], selectedSoftwareBuilds: SoftwareBuildListResponse[], isActive: boolean, resourceName: string): Promise<void> {
    try {
      // Validate user permissions and country access
      if (!this.validateSoftwareBuildPermissions(softwareBuildIds, selectedSoftwareBuilds, resourceName)) {
        return;
      }

      const confirmed = await this.softWareBuildConformationService.openInventoryOperationModel(
        "Confirm",
        "Cancel",
        "Confirmation",
        "Are you sure you want to mark Software Build(s) as " + (isActive ? "Active?" : "Inactive? Doing same will remove the release association with test device if any.")
      );

      if (!confirmed) {
        return;
      }

      this.isLoading(true, resourceName);
      const response = await firstValueFrom(this.softwareBuildApiCallService.markInventoriesActiveInactive(softwareBuildIds, { active: isActive }));
      this.showSuccessAndRefresh(response.body['message'], resourceName);
      return;

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      this.isLoading(false, resourceName);
      return;
    }
  }

  /**
  * Validate software build selection and permissions
  * <AUTHOR>
  * @param softwareBuildIds - Array of software build IDs
  * @param selectedSoftwareBuilds - Array of selected software build objects
  * @param resourceName - Resource name for validation
  * @returns true if validation passes, false otherwise
  */
  private validateSoftwareBuildPermissions(softwareBuildIds: number[], selectedSoftwareBuilds: SoftwareBuildListResponse[], resourceName: string): boolean {
    if (softwareBuildIds.length === 0) {
      this.toastrService.info("Please Select Software Build(s)");
      return false;
    }

    // Get associated countries for validation
    const itemAssociatedCountryList = this.getItemAssociatedCountryList(selectedSoftwareBuilds);
    return this.moduleValidationService.validateWithUserCountryForMultileRecord(itemAssociatedCountryList, resourceName, true);
  }

  /**
  * Get Item Associated Country List
  * <AUTHOR>
  * @param selectedSoftwareBuilds - Array of selected software build objects
  * @returns Array of associated country names
  */
  private getItemAssociatedCountryList(selectedSoftwareBuilds: SoftwareBuildListResponse[]): string[] {
    let itemAssociatedCountryList: Array<string> = [];
    for (let softwareBuild of selectedSoftwareBuilds) {
      if (softwareBuild.countries && softwareBuild.countries.length > 0) {
        itemAssociatedCountryList = softwareBuild.countries;
      }
    }
    return itemAssociatedCountryList;
  }

  /**
  * Show success message and refresh page based on resource type
  * <AUTHOR>
  * @param message - Success message to display
  * @param resourceName - Resource name for determining refresh action
  */
  private showSuccessAndRefresh(message: string, resourceName: string): void {
    this.isLoading(false, resourceName);
    this.toastrService.success(message);
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    this.callRefreshPageSubject(listingPageReloadSubjectParameter, resourceName, false, null);
  }

}
