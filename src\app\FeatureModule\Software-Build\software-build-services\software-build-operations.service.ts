import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { SoftwareBuildListResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { SoftwareBuildSearchRequestBody } from 'src/app/model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { SoftwareBuildFilterAction } from 'src/app/model/SoftwaarBuilds/SoftwareFilterAction.model';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';

@Injectable({
  providedIn: 'root'
})
export class SoftwareBuildOperationsService {

  constructor() { }

  //Refresh Doftware Build List
  private softwareBuildListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Role list filter
  private softwareBuildListFilterRequestParameterSubject = new Subject<SoftwareBuildFilterAction>();

  private jsonVersionList: Array<Jsonlist> = [];
  private countryList: Array<CountryListResponse> = [];


  /**
  * Software Build List Page Refresh After some Action Like Create or Update or Delete Device
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getSoftwareBuildListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.softwareBuildListRefreshSubject;
  }

  public callSoftwareBuildListFilterRequestParameterSubject(softwareBuildFilterAction: SoftwareBuildFilterAction): void {
    this.softwareBuildListFilterRequestParameterSubject.next(softwareBuildFilterAction);
  }

  /**
   * Role List Page Refresh After some Action Like Serch parameter add
   * Note : Create or Update or Delete Role After Clear All filter and refresh page 
   * <AUTHOR>
   * @returns RoleList 
   */
  public getSoftwareBuildListFilterRequestParameterSubject(): Subject<SoftwareBuildFilterAction> {
    return this.softwareBuildListFilterRequestParameterSubject;
  }

  /**
  * This function call the subject for reload the page data
  *  Note : (ListRoleResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param isReloadData -> false means move to prev page for DetailRoleResource.
  * @param resourceName 
  */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean): void {
    if (resourceName == SoftwareBuildListResource) {
      if (isFilterHidden) {
        let softwareBuildRequestBody = new SoftwareBuildSearchRequestBody("", [], null, null, null, null);
        let softwareBuildFilterAction = new SoftwareBuildFilterAction(listingPageReloadSubjectParameter, softwareBuildRequestBody);
        this.callSoftwareBuildListFilterRequestParameterSubject(softwareBuildFilterAction);
      } else {
        this.softwareBuildListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    }
  }

  /**
  * Set JsonVersionList
  */
  public setJsonVersionList(jsonVersionList: Array<Jsonlist>): void {
    this.jsonVersionList = jsonVersionList;
  }

  /**
   * Get JsonVersionList
   */
  public getJsonVersionList(): Array<Jsonlist> {
    return this.jsonVersionList;
  }

  /**
    * Set Country List
    * <AUTHOR>
    * @param countryList - Array of countries to cache
    */
  public setCountryList(countryList: CountryListResponse[]): void {
    this.countryList = countryList;
  }

  /**
  * Get Country List
  * <AUTHOR>
  * @returns Cached array of countries
  */
  public getCountryList(): CountryListResponse[] {
    return this.countryList;
  }

}
