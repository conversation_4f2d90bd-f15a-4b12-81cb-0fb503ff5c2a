import { DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { isUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { SoftwareBuildFilterAction } from 'src/app/model/SoftwaarBuilds/SoftwareFilterAction.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { ITEMS_PER_PAGE, SoftwareBuildListResource } from '../../../app.constants';
import { CountryListResponse } from '../../../model/Country/CountryListResponse.model';
import { SoftwareBuildListResponse } from '../../../model/SoftwaarBuilds/SoftwareBuildListResponse.model';
import { SoftwareBuildSearchRequestBody } from '../../../model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { BasicModelConfig } from '../../../model/common/BasicModelConfig.model';
import { SuccessMessageResponse } from '../../../model/common/SuccessMessageResponse.model';
import { Jsonlist } from '../../../model/video/jsonlist.model';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { PermissionAction } from '../../../shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from '../../../shared/enum/collapseFilterButtonText.enum';
import { PermissionService } from '../../../shared/permission.service';
import { UploadScanService } from '../../../shared/upload-scan.service';
import { CommonCheckboxService } from '../../../shared/util/common-checkbox.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { VideoService } from '../../../shared/videoservice/video.service';
import { DeleteSoftwareBildDialogService } from '../software-build-services/delete-software-build-dialog/delete-software-build-dialog.service';
import { EditSoftwareBuildDialogService } from '../software-build-services/edit-software-build-dialog/edit-software-build-dialog.service';
import { SoftwareBuildApiCallService } from '../software-build-services/software-api-call/software-build-api-call.service';
import { SoftwareBuildOperationsService } from '../software-build-services/software-build-operations.service';

@Component({
  selector: 'app-software-build-list',
  templateUrl: './software-build-list.component.html',
  styleUrls: ['./software-build-list.component.css']
})
export class SoftwareBuildListComponent implements OnInit {

  // Input/Output properties for communication with parent module
  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("softwareBuildListFilterRequestBody") sofwareBuildListFilterRequestBody: SoftwareBuildSearchRequestBody;
  @Input("isFilterHidden") isFilterHidden: boolean;

  // Output events to communicate changes back to parent module
  @Output('isFilterComponentInitWithApicallChange') isFilterComponentInitWithApicallChange = new EventEmitter<boolean>();
  @Output('softwareBuildListFilterRequestBodyChange') softwareBuildListFilterRequestBodyChange = new EventEmitter<SoftwareBuildSearchRequestBody>();
  @Output('isFilterHiddenChange') isFilterHiddenChange = new EventEmitter<boolean>();

  loading = false;
  createdDt: any;
  itemsPerPage: number;
  page: number = 0;
  previousPage: number;
  inventory: Array<SoftwareBuildListResponse> = [];

  drpselectsize: number = ITEMS_PER_PAGE;

  totalItems: number;
  closeResult: string;
  modalTable: SoftwareBuildListResponse[];
  totalItemInventoryDisplay: number = 0;
  totalItemInventory: number = 0;
  deviceTypes: string[];
  inventoryStatus: string[];
  countryList: CountryListResponse[] = [];
  jsonVersionList: Array<Jsonlist> = [];

  inventoryIdList = [];
  localInventoryList: SoftwareBuildListResponse[];
  seletedinventoryIdWithCountry: Map<number, Array<string>> = new Map();

  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;
  userAssociatedCountrys: CountryListResponse[] = [];

  //Role Serach Request
  softwareBuildSearchRequestBody: SoftwareBuildSearchRequestBody = null;
  listPageRefreshForbackToDetailPage: boolean = false;

  // show entry selection
  dataSizes: string[] = [];

  //unique CheckBox Name
  chkPreFix = "itemInventory";
  selectAllCheckboxId = "selectAllitemInventory";
  checkboxListName = "itemInventory[]";

  //Operation List
  operationsList: string[] = [];

  subscriptionForRoleListFilterRequestParameter: Subscription;

  //Permission
  deleteSoftwareBuildPermission: boolean = false;
  uploadSoftwareBuildPermission: boolean = false;
  updateSoftwareBuildPermission: boolean = false;

  //Default options
  softwareBuildOperations: string = "Software Build Operations";

  //subscription
  subscriptionForLoading: Subscription;
  subscriptionForSoftwareBuildListLoading: Subscription;

  constructor(
    private softwareBuildApiCallService: SoftwareBuildApiCallService,
    private datePipe: DatePipe,
    private uploadScanService: UploadScanService,
    private editSoftwareBuildDialogService: EditSoftwareBuildDialogService,
    private permissionService: PermissionService,
    private toste: ToastrService,
    private exceptionService: ExceptionHandlingService,
    private authservice: AuthJwtService,
    private deleteSoftwareBildDialogService: DeleteSoftwareBildDialogService,
    private videoService: VideoService,
    private commonsService: CommonsService,
    private downloadService: DownloadService,
    private commonCheckboxService: CommonCheckboxService,
    private commonOperationsService: CommonOperationsService,
    private softwareBuildOperationService: SoftwareBuildOperationsService
  ) {
    this.itemsPerPage = ITEMS_PER_PAGE;
  }

  ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.page = 0;
      this.dataSizes = this.commonsService.accessDataSizes();
      this.operationsList = this.commonOperationsService.accessSoftwareBuildOperations();
      this.isFilterComponentInitWithApicall = true;
      this.listPageRefreshForbackToDetailPage = false;
      this.isFilterHidden = false;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.previousPage = 1;
      this.inventoryIdList = []
      this.setItemInventoryPermission();
      this.refreshFilter();
    }
    this.subjectInit()
  }

  /**
  * Destroy subscription
  *
  * <AUTHOR>
  */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForLoading)) { this.subscriptionForLoading.unsubscribe() }
    if (!isUndefined(this.subscriptionForSoftwareBuildListLoading)) { this.subscriptionForSoftwareBuildListLoading.unsubscribe() }
  }

  private subjectInit(): void {
    /**
     * Software Build List Loading subscription (following probe-list pattern)
     * <AUTHOR>
     */
    this.subscriptionForSoftwareBuildListLoading = this.softwareBuildOperationService.getSoftwareBuildListLoadingSubject()?.subscribe((status: boolean) => {
      this.loading = status;
    });

    /**
     * Loading Hide/Display
     * <AUTHOR>
     */
    this.subscriptionForLoading = this.downloadService.getisLoadingSubject()?.subscribe((res: boolean) => {
      this.loading = res;
    });

    /**
         * This Subject call from Filter component
         * Load all the Data
         * <AUTHOR>
         */
    this.subscriptionForRoleListFilterRequestParameter = this.softwareBuildOperationService.getSoftwareBuildListFilterRequestParameterSubject().subscribe((softwareBuildRequestParameter: SoftwareBuildFilterAction) => {
      if (softwareBuildRequestParameter.listingPageReloadSubjectParameter.isReloadData) {
        if (softwareBuildRequestParameter.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.inventoryIdList = [];
          this.seletedinventoryIdWithCountry.clear();
          this.resetPage()
        }
        this.loadAll(softwareBuildRequestParameter.softwareBuildSearchRequestBody);
      }
    });
  }

  /**
   * set Item Inventory
   */
  private setItemInventoryPermission(): void {
    this.deleteSoftwareBuildPermission = this.permissionService.getSoftwearBuildPermission(PermissionAction.DELETE_SOFTWARE_BUILD_ACTION);
    this.uploadSoftwareBuildPermission = this.permissionService.getSoftwearBuildPermission(PermissionAction.UPLOAD_SOFTWARE_BUILD_ACTION);
    this.updateSoftwareBuildPermission = this.permissionService.getSoftwearBuildPermission(PermissionAction.UPDATE_SOFTWARE_BUILD_ACTION);
  }

  /**
    * Call Filter component subject and reload page
    * <AUTHOR>
    * @param isDefaultPageNumber 
    * @param isClearFilter 
    */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false)
    this.softwareBuildOperationService.callRefreshPageSubject(listingPageReloadSubjectParameter, SoftwareBuildListResource, this.isFilterHidden, this.softwareBuildSearchRequestBody);
  }

  /**
  * Clear all filter ,Reset Page and Reload the page
  * <AUTHOR>
  */
  public refreshFilter(): void {
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, true);
  }

  /**
  * Reset Page
  * <AUTHOR>
  */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
  * Refresh button click
  * 
  * <AUTHOR>
  */
  public clickOnRefreshButton(): void {
    this.page = 0;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  changeDataSize(datasize: any): void {
    this.loading = true;
    this.itemsPerPage = datasize.target.value;
    this.reloadItem();
  }

  public reloadItem(): void {
    this.inventoryIdList = [];
    this.page = 0;
    this.filterPageSubjectCallForReloadPage(false, false);
  }

  /**
   * Software Build List API call using software build operation service
   * <AUTHOR>
   */
  public async loadAll(softwareBuildSearchRequestBody: SoftwareBuildSearchRequestBody): Promise<void> {
    this.loading = true;
    this.softwareBuildListFilterRequestBodyChange.emit(softwareBuildSearchRequestBody);
    this.softwareBuildSearchRequestBody = softwareBuildSearchRequestBody;

    const pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage,
    };

    const result = await this.softwareBuildOperationService.loadSoftwareBuildList(softwareBuildSearchRequestBody, pageObj);

    if (result.success) {
      this.inventory = result.softwareBuilds;
      this.totalItemInventoryDisplay = result.totalSoftwareBuildDisplay;
      this.totalItemInventory = result.totalSoftwareBuilds;
      this.localInventoryList = result.localSoftwareBuildList;
      this.totalItems = result.totalItems;
      this.page = result.page;
    } else {
      this.inventory = [];
      this.totalItemInventoryDisplay = 0;
      this.totalItemInventory = 0;
      this.localInventoryList = [];
      this.totalItems = 0;
    }

    this.loading = false;
    this.defaultSelectAll();
  }

  loadPage(page: number): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.commonCheckboxService.clearSelectAllCheckbox(this.selectAllCheckboxId);
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }




  public changeInventoryOperation(event: any): void {
    const operationName = event.target.value;

    // Get selected software builds for validation
    const selectedSoftwareBuilds = this.getSelectedSoftwareBuilds();

    // All operations are now handled by centralized operations service
    this.softwareBuildOperationService.changeOperationForSoftwareBuild(operationName, SoftwareBuildListResource, this.inventoryIdList, selectedSoftwareBuilds);

    let selection = document.getElementById('inventoryOperation') as HTMLSelectElement;
    if (selection) {
      selection.value = this.softwareBuildOperations;
    }
  }

  /**
  * Get selected software builds for validation
  * <AUTHOR>
  * @returns Array of selected software build objects
  */
  private getSelectedSoftwareBuilds(): SoftwareBuildListResponse[] {
    const selectedSoftwareBuilds: SoftwareBuildListResponse[] = [];
    for (let id of this.inventoryIdList) {
      const softwareBuild = this.localInventoryList.find(item => item.id === id);
      if (softwareBuild) {
        selectedSoftwareBuilds.push(softwareBuild);
      }
    }
    return selectedSoftwareBuilds;
  }

  onChangeInventory(id: number, countryMaster: Array<string>, event: any) {
    if (event.target.checked) {
      this.inventoryIdList.push(id);
      this.seletedinventoryIdWithCountry.set(id, countryMaster);
    }
    else {
      let index = this.inventoryIdList.findIndex(obj => obj == id);
      this.inventoryIdList.splice(index, 1);
      this.seletedinventoryIdWithCountry.delete(id);
    }
    this.defaultSelectAll();
  }

  /**
  * select All checkbox select or deSelect
  * <AUTHOR>
  */
  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localInventoryList.map(i => i.id), this.inventoryIdList, this.selectAllCheckboxId);
  }

  /**
  * Clear all software build selections
  * <AUTHOR>
  */
  public clearSoftwareBuildSelections(): void {
    this.inventoryIdList = [];
    this.seletedinventoryIdWithCountry.clear();

    // Clear individual checkboxes
    let softwareBuildCheckboxes = (<HTMLInputElement[]><any>document.getElementsByName(this.checkboxListName));
    let checkboxLength = softwareBuildCheckboxes.length;
    for (let index = 0; index < checkboxLength; index++) {
      softwareBuildCheckboxes[index].checked = false;
    }

    // Clear select all checkbox
    let selectAllCheckbox = <HTMLInputElement>document.getElementById(this.selectAllCheckboxId);
    if (selectAllCheckbox != null) {
      selectAllCheckbox.checked = false;
    }
  }

  public selectAllItem(isChecked: boolean): void {
    this.inventoryIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localInventoryList.map(i => i.id), this.inventoryIdList, this.checkboxListName);

    if (isChecked) {
      for (let inventory of this.localInventoryList) {
        let inventoryindex = this.inventoryIdList.findIndex(id => id == inventory);
        if (inventoryindex < 0) {

          this.seletedinventoryIdWithCountry.set(inventory.id, inventory.countries);
        }
      }
    }
    else {
      for (let inventoryObj of this.localInventoryList) {
        this.seletedinventoryIdWithCountry.delete(inventoryObj.id);
      }
    }
  }

  /**
   * Get Attachment URL using operations service
   * <AUTHOR>
   * @param id
   */
  public async getAttachmentUrl(id: number): Promise<void> {
    try {
      const url = await this.softwareBuildOperationService.downloadAttachment(id, "attachment", SoftwareBuildListResource);
      await this.downloadMyFileAsync(url);
    } catch (error) {
      // Error handling is done in the service
    }
  }

  /**
   * Get Release Notes using operations service
   * <AUTHOR>
   * @param id
   */
  public async getReleaseNoteUrl(id: number): Promise<void> {
    try {
      const url = await this.softwareBuildOperationService.downloadAttachment(id, "releaseNote", SoftwareBuildListResource);
      await this.downloadMyFileAsync(url);
    } catch (error) {
      // Error handling is done in the service
    }
  }

  /**
   * Download File
   * <AUTHOR>
   * @param url
   */
  public async downloadMyFileAsync(url: string): Promise<void> {
    await this.downloadService.downloadMyFile(url);
  }

  /**
   * Delete software build using operations service
   * <AUTHOR>
   * @param inventoryItemId
   * @param itemNumber
   */
  public async deleteItem(inventoryItemId: number, itemNumber: string): Promise<void> {
    await this.softwareBuildOperationService.deleteSoftwareBuild(inventoryItemId, itemNumber, SoftwareBuildListResource);
  }

  /**
   * Upload Firmware manually
   */
  public confirmDownloadDataset(): void {
    this.uploadScanService.confirm('Upload Firmware', 'Please confirm to upload.', this.userAssociatedCountrys, this.jsonVersionList)
      .then((confirmed: boolean) => {
        if (confirmed) {
          this.loading = true;
          this.reloadItem();
        } else {
          this.loading = false;
        }
      });
  }


  /**
  * Toggle Filter
  * 
  */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * Edit Inventory
   * 
   * <AUTHOR>
   * @param inventory 
   */
  public editInventoryDetails(inventory: SoftwareBuildListResponse): void {
    let basicModelConfig: BasicModelConfig = new BasicModelConfig('Edit Software Build', null, 'Update', 'Cancel');
    this.editSoftwareBuildDialogService.openEditInventoryModel(basicModelConfig, inventory, this.jsonVersionList, this.userAssociatedCountrys)
      .then((confirmed: boolean) => {
        if (confirmed) {
          this.reloadItem();
        }
      });
  }

  public downloadJSONFile(jsonMaster: Jsonlist): void {
    this.loading = true;
    this.videoService.downloadJSONFile(jsonMaster?.id)?.subscribe({
      next: (downloadJsonResponse) => {
        let jsonData = downloadJsonResponse.body["data"];
        const blob = new Blob([JSON.stringify(jsonData)], { type: 'application/json' });
        let downloadLink = document.createElement('a');
        downloadLink.href = window.URL.createObjectURL(new Blob([blob], { type: blob.type }));
        downloadLink.setAttribute('download', `${jsonMaster.version}.json`);
        document.body?.appendChild(downloadLink);
        downloadLink.click();
        this.loading = false;
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }
}
